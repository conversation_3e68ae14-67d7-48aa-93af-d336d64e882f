<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Livewire\Dashboard\Widgets\Navigation\FrequencyGenerator;
use App\Services\Calculation\FrequencyService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class FrequencyGeneratorTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock user authentication
        $user = new \stdClass();
        $user->id = 1;
        Auth::shouldReceive('id')->andReturn(1);
        Auth::shouldReceive('user')->andReturn($user);
        
        // Mock user details function
        if (!function_exists('getUserDetails')) {
            function getUserDetails() {
                return (object) [
                    'thema_speichern' => 'test topic'
                ];
            }
        }
        
        // Mock biorhythm details function
        if (!function_exists('biorythVisibleDetails')) {
            function biorythVisibleDetails() {
                return (object) [
                    'gs_min_price' => 5,
                    'gs_max_price' => 300
                ];
            }
        }
        
        // Mock calculation frequency function
        if (!function_exists('calculationFrequency')) {
            function calculationFrequency($topic) {
                return strlen($topic) * 50; // Simple mock calculation
            }
        }
    }

    public function test_topic_text_update_updates_both_frequency_and_time(): void
    {
        $component = Livewire::test(FrequencyGenerator::class, [
            'poolId' => 1,
            'widget' => ['id' => 'test']
        ]);

        // Set initial empty state
        $component->set('topicText', '');
        $component->assertSet('frequencyHz', '');
        $component->assertSet('frequencyTime', '');

        // Update topic text
        $component->set('topicText', 'test topic');

        // Assert that both frequency and time are updated
        $component->assertSet('frequencyHz', function($value) {
            return !empty($value) && is_numeric($value);
        });
        
        $component->assertSet('frequencyTime', function($value) {
            return !empty($value) && is_numeric($value);
        });
    }

    public function test_empty_topic_text_clears_frequency_and_time(): void
    {
        $component = Livewire::test(FrequencyGenerator::class, [
            'poolId' => 1,
            'widget' => ['id' => 'test']
        ]);

        // Set some values first
        $component->set('topicText', 'test topic');
        $component->set('frequencyHz', '440');
        $component->set('frequencyTime', '30');

        // Clear topic text
        $component->set('topicText', '');

        // Assert that both frequency and time are cleared
        $component->assertSet('frequencyHz', '');
        $component->assertSet('frequencyTime', '');
    }

    public function test_frequency_service_integration(): void
    {
        $component = Livewire::test(FrequencyGenerator::class, [
            'poolId' => 1,
            'widget' => ['id' => 'test']
        ]);

        // Test that FrequencyService is properly integrated
        $component->set('topicText', 'integration test');
        
        // The frequency should be calculated based on our mock function
        $expectedFrequency = strlen('integration test') * 50; // 16 * 50 = 800
        
        // Since the service applies transformations, we just check it's not empty
        $component->assertSet('frequencyHz', function($value) {
            return !empty($value) && is_numeric($value) && $value > 0;
        });
    }

    public function test_validation_rules_are_applied(): void
    {
        $component = Livewire::test(FrequencyGenerator::class, [
            'poolId' => 1,
            'widget' => ['id' => 'test']
        ]);

        // Test validation for empty topic
        $component->set('topicText', '');
        $component->set('frequencyHz', '440');
        $component->set('frequencyTime', '30');
        $component->call('addToCart');
        $component->assertHasErrors(['topicText']);

        // Test validation for invalid frequency
        $component->set('topicText', 'test');
        $component->set('frequencyHz', '100'); // Below minimum
        $component->set('frequencyTime', '30');
        $component->call('addToCart');
        $component->assertHasErrors(['frequencyHz']);

        // Test validation for invalid time
        $component->set('topicText', 'test');
        $component->set('frequencyHz', '440');
        $component->set('frequencyTime', '1'); // Below minimum
        $component->call('addToCart');
        $component->assertHasErrors(['frequencyTime']);
    }
}
